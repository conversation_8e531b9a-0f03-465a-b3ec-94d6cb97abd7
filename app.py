from flask import Flask, render_template, jsonify, request, redirect, url_for
import threading
import websocket
import time
import json
from datetime import datetime, timezone, timedelta
import os
import queue
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed

# 定义北京时区
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_time():
    """获取北京时间"""
    return datetime.now(BEIJING_TZ)

# 导入配置
try:
    from config import (
        MAX_CONCURRENT_THREADS,
        DETECTION_TIME,
        AUTO_DETECTION_INTERVAL,
        MANUAL_DETECTION_THREADS,
        SERVER_PORT,
        SERVER_HOST,
        DEBUG_MODE
    )
    print("已加载配置文件 config.py")
except ImportError:
    # 如果没有配置文件，使用默认值
    MAX_CONCURRENT_THREADS = 10
    DETECTION_TIME = 8
    AUTO_DETECTION_INTERVAL = 180
    MANUAL_DETECTION_THREADS = 15
    SERVER_PORT = 4000
    SERVER_HOST = '0.0.0.0'
    DEBUG_MODE = False
    print("未找到配置文件，使用默认配置")

app = Flask(__name__)

# 全局变量
server_statuses = {}  # 存储服务器状态
active_servers = []  # 存储活跃服务器列表
detection_results = []  # 存储检测结果
is_detecting = False  # 检测状态标志
stop_event = threading.Event()  # 停止事件
detection_thread = None  # 检测线程

class ServerDetector:
    def __init__(self):
        self.message_counts = defaultdict(int)  # 消息计数器
        self.detection_start_time = None
        
    def load_servers(self):
        """从server.txt加载服务器列表"""
        servers = []
        
        # 优先使用活跃的服务器文件
        for filename in ["active_servers.txt", "websocket_working_servers.txt", 
                        "available_servers.txt", "server.txt"]:
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    servers = [line.strip() for line in f.readlines() if line.strip()]
                print(f"从{filename}中读取到 {len(servers)} 个服务器")
                break
        
        return servers
    
    def test_server_connection(self, server, timeout=10):
        """测试单个服务器连接并计算消息数"""
        message_count = 0
        connected = False
        
        def on_open(ws):
            nonlocal connected
            connected = True
            print(f"成功连接到 {server}")
        
        def on_message(ws, message):
            nonlocal message_count
            message_count += 1
        
        def on_error(ws, error):
            print(f"连接 {server} 时出错: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print(f"连接到 {server} 已关闭")
        
        try:
            # 尝试WS连接
            server_str = str(server).strip()
            if ":" in server_str:
                host = server_str.split(":")[0]
                port = server_str.split(":")[1]
            else:
                host = server_str
                port = "9000"
            
            ws_url = f"ws://{host}:{port}"
            
            ws = websocket.WebSocketApp(
                ws_url,
                on_open=on_open,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close
            )
            
            # 运行连接，限制时间
            ws.run_forever(ping_interval=30, ping_timeout=10)
            
        except Exception as e:
            # 如果WS失败，尝试WSS
            try:
                wss_url = f"wss://{server_str}"
                ws = websocket.WebSocketApp(
                    wss_url,
                    on_open=on_open,
                    on_message=on_message,
                    on_error=on_error,
                    on_close=on_close
                )
                ws.run_forever(sslopt={"cert_reqs": 0}, ping_interval=30, ping_timeout=10)
            except Exception as e2:
                print(f"连接 {server} 失败: {e2}")
        
        return message_count, connected
    
    def detect_active_servers(self, detection_time=10, max_workers=20, batch_size=None):
        """检测活跃服务器（检测指定时间）- 使用线程池优化性能"""
        global is_detecting, active_servers, detection_results

        is_detecting = True
        self.detection_start_time = get_beijing_time()
        servers = self.load_servers()

        if not servers:
            is_detecting = False
            return []

        # 如果服务器数量太多，使用分批处理
        if batch_size is None:
            batch_size = max_workers * 5  # 默认每批处理5倍于并发数的服务器，减少超时风险

        print(f"开始检测 {len(servers)} 个服务器，检测时间: {detection_time}秒，最大并发: {max_workers}")
        if len(servers) > batch_size:
            print(f"使用分批处理，每批 {batch_size} 个服务器")

        # 重置消息计数器
        self.message_counts.clear()

        def test_server_with_timeout(server):
            """带超时的服务器测试"""
            start_time = time.time()
            message_count = 0
            connected = False

            def on_message_wrapper(ws, message):
                nonlocal message_count
                # 只在检测时间内计数
                if time.time() - start_time < detection_time:
                    message_count += 1

            def on_open_wrapper(ws):
                nonlocal connected
                connected = True
                print(f"连接到 {server}")

            def on_error_wrapper(ws, error):
                pass  # 静默处理错误

            def on_close_wrapper(ws, close_status_code, close_msg):
                pass  # 静默处理关闭

            try:
                server_str = str(server).strip()
                if ":" in server_str:
                    host = server_str.split(":")[0]
                    port = server_str.split(":")[1]
                else:
                    host = server_str
                    port = "9000"

                ws_url = f"ws://{host}:{port}"

                ws = websocket.WebSocketApp(
                    ws_url,
                    on_open=on_open_wrapper,
                    on_message=on_message_wrapper,
                    on_error=on_error_wrapper,
                    on_close=on_close_wrapper
                )

                # 使用超时控制连接时间
                start_connect_time = time.time()

                def run_with_timeout():
                    try:
                        ws.run_forever(ping_interval=30, ping_timeout=5)
                    except:
                        pass

                # 启动连接
                connect_thread = threading.Thread(target=run_with_timeout, daemon=True)
                connect_thread.start()

                # 等待检测时间
                connect_thread.join(timeout=detection_time)

                # 强制关闭连接
                try:
                    ws.close()
                except:
                    pass

            except Exception as e:
                # 尝试WSS连接
                try:
                    wss_url = f"wss://{server_str}"
                    ws = websocket.WebSocketApp(
                        wss_url,
                        on_open=on_open_wrapper,
                        on_message=on_message_wrapper,
                        on_error=on_error_wrapper,
                        on_close=on_close_wrapper
                    )

                    def run_with_timeout():
                        try:
                            ws.run_forever(sslopt={"cert_reqs": 0}, ping_interval=30, ping_timeout=5)
                        except:
                            pass

                    connect_thread = threading.Thread(target=run_with_timeout, daemon=True)
                    connect_thread.start()
                    connect_thread.join(timeout=detection_time)

                    try:
                        ws.close()
                    except:
                        pass
                except:
                    pass

            # 返回结果
            return server, message_count, connected

        # 分批处理服务器以避免超时
        total_completed = 0

        for batch_start in range(0, len(servers), batch_size):
            if stop_event.is_set():
                break

            batch_end = min(batch_start + batch_size, len(servers))
            batch_servers = servers[batch_start:batch_end]
            batch_num = (batch_start // batch_size) + 1
            total_batches = (len(servers) + batch_size - 1) // batch_size

            print(f"处理第 {batch_num}/{total_batches} 批，服务器 {batch_start+1}-{batch_end}")

            # 使用线程池处理当前批次
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交当前批次的任务
                future_to_server = {executor.submit(test_server_with_timeout, server): server
                                  for server in batch_servers if not stop_event.is_set()}

                # 为当前批次设置合理的超时时间
                batch_timeout = detection_time + 30  # 每批次额外30秒缓冲

                # 收集当前批次的结果
                batch_completed = 0
                try:
                    for future in as_completed(future_to_server, timeout=batch_timeout):
                        if stop_event.is_set():
                            break
                        try:
                            server, message_count, connected = future.result(timeout=5)
                            if message_count > 0:
                                self.message_counts[server] = message_count
                            batch_completed += 1
                            total_completed += 1

                        except Exception as e:
                            # 忽略单个服务器的错误
                            batch_completed += 1
                            total_completed += 1

                except Exception as e:
                    print(f"批次 {batch_num} 处理出错: {e}")
                    # 取消未完成的任务
                    for future in future_to_server:
                        if not future.done():
                            future.cancel()
                    batch_completed = len(batch_servers)
                    total_completed += batch_completed

                print(f"批次 {batch_num} 完成: {batch_completed}/{len(batch_servers)} 个服务器")

            # 批次间短暂休息，减少服务器压力
            if batch_num < total_batches and not stop_event.is_set():
                time.sleep(2)

        print(f"所有批次检测完成，共处理 {total_completed}/{len(servers)} 个服务器")

        # 筛选出消息数超过10的服务器
        active_servers = []
        for server, count in self.message_counts.items():
            if count > 10:
                active_servers.append({
                    'server': server,
                    'message_count': count,
                    'detection_time': self.detection_start_time.strftime("%Y-%m-%d %H:%M:%S")
                })

        # 按消息数排序
        active_servers.sort(key=lambda x: x['message_count'], reverse=True)

        # 保存检测结果
        detection_results.append({
            'timestamp': self.detection_start_time.strftime("%Y-%m-%d %H:%M:%S"),
            'total_servers': len(servers),
            'active_servers': len(active_servers),
            'servers': active_servers.copy()
        })

        # 只保留最近10次检测结果
        if len(detection_results) > 10:
            detection_results.pop(0)

        print(f"检测完成，发现 {len(active_servers)} 个活跃服务器（消息数>10）")
        is_detecting = False

        return active_servers

def auto_detection_loop():
    """自动检测循环：根据配置间隔检测，减少服务器负载"""
    detector = ServerDetector()

    while not stop_event.is_set():
        try:
            print(f"开始自动检测... (并发线程: {MAX_CONCURRENT_THREADS}, 检测时间: {DETECTION_TIME}秒)")
            # 使用配置的参数来减少服务器负载，自动检测使用较小的批次
            detector.detect_active_servers(
                detection_time=DETECTION_TIME,
                max_workers=MAX_CONCURRENT_THREADS,
                batch_size=MAX_CONCURRENT_THREADS * 4  # 自动检测使用更小批次
            )
            print(f"检测完成，等待下次检测... (间隔: {AUTO_DETECTION_INTERVAL}秒)")

            # 等待配置的间隔时间
            for _ in range(AUTO_DETECTION_INTERVAL):
                if stop_event.is_set():
                    break
                time.sleep(1)

        except Exception as e:
            print(f"自动检测出错: {e}")
            time.sleep(30)  # 出错后等待30秒再重试

@app.route('/')
def index():
    """主页 - 检测HTTPS访问并重定向到HTTP"""
    # 检测是否为HTTPS访问
    if request.is_secure or request.headers.get('X-Forwarded-Proto') == 'https':
        #重定向到 http://sjz.exacg.cc   
        http_url = 'http://sjz.exacg.cc'
        print(f"检测到HTTPS访问，重定向到: {http_url}")
        return redirect(http_url, code=301)  # 使用301永久重定向

    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    return jsonify({
        'is_detecting': is_detecting,
        'active_servers_count': len(active_servers),
        'last_detection': detection_results[-1]['timestamp'] if detection_results else None
    })

@app.route('/api/active_servers')
def get_active_servers():
    """获取活跃服务器列表"""
    return jsonify(active_servers)

@app.route('/api/detection_history')
def get_detection_history():
    """获取检测历史"""
    return jsonify(detection_results)

@app.route('/api/config')
def get_config():
    """获取当前配置信息"""
    return jsonify({
        'max_concurrent_threads': MAX_CONCURRENT_THREADS,
        'detection_time': DETECTION_TIME,
        'auto_detection_interval': AUTO_DETECTION_INTERVAL,
        'manual_detection_threads': MANUAL_DETECTION_THREADS,
        'performance_tips': {
            'low_performance': '建议设置: 并发线程5-10, 检测时间6-8秒',
            'medium_performance': '建议设置: 并发线程10-20, 检测时间8-12秒',
            'high_performance': '建议设置: 并发线程20-50, 检测时间10-15秒'
        }
    })

@app.route('/api/start_detection')
def start_detection():
    """手动开始检测"""
    global detection_thread

    if not is_detecting:
        detector = ServerDetector()
        detection_thread = threading.Thread(
            target=detector.detect_active_servers,
            kwargs={
                'detection_time': DETECTION_TIME,
                'max_workers': MANUAL_DETECTION_THREADS,
                'batch_size': MANUAL_DETECTION_THREADS * 6  # 手动检测使用中等批次
            },
            daemon=True
        )
        detection_thread.start()
        return jsonify({'status': 'started', 'config': {
            'detection_time': DETECTION_TIME,
            'max_workers': MANUAL_DETECTION_THREADS
        }})
    else:
        return jsonify({'status': 'already_running'})

if __name__ == '__main__':
    # 启动自动检测线程
    auto_thread = threading.Thread(target=auto_detection_loop, daemon=True)
    auto_thread.start()

    print("=" * 60)
    print("三角洲地图扫描网页服务器已启动")
    print(f"访问 http://localhost:{SERVER_PORT} 查看监控界面")
    print("=" * 60)
    print("当前性能配置:")
    print(f"  最大并发线程数: {MAX_CONCURRENT_THREADS}")
    print(f"  检测时间: {DETECTION_TIME}秒")
    print(f"  自动检测间隔: {AUTO_DETECTION_INTERVAL}秒")
    print(f"  手动检测线程数: {MANUAL_DETECTION_THREADS}")
    print("=" * 60)
    print("性能优化提示:")
    print("  如果出现 'can't start new thread' 错误，请:")
    print("  1. 减少 MAX_CONCURRENT_THREADS (建议5-10)")
    print("  2. 减少 DETECTION_TIME (建议6-8秒)")
    print("  3. 增加 AUTO_DETECTION_INTERVAL (建议300秒以上)")
    print("=" * 60)

    try:
        app.run(host=SERVER_HOST, port=SERVER_PORT, debug=DEBUG_MODE)
    except KeyboardInterrupt:
        print("正在关闭服务器...")
        stop_event.set()
